import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

export class ReactWebviewProvider implements vscode.WebviewViewProvider {
    private _view?: vscode.WebviewView;

    constructor(
        private readonly _extensionUri: vscode.Uri,
        private readonly _viewId: string
    ) { }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        console.log(`Resolving webview view for ${this._viewId}...`);
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionUri
            ]
        };

        try {
            const html = this.getHtmlForWebview(webviewView.webview);
            console.log('Generated HTML length:', html.length);
            console.log('HTML preview:', html);
            webviewView.webview.html = html;
            console.log('Webview HTML set successfully');
        } catch (error) {
            console.error('Error setting webview HTML:', error);
            // 提供一个简单的fallback
            webviewView.webview.html = `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>AI Code</title>
                </head>
                <body>
                    <h1>AI Code Extension</h1>
                    <p>Webview加载成功！</p>
                    <button onclick="testMessage()">测试消息</button>
                    <script>
                        const vscode = acquireVsCodeApi();
                        function testMessage() {
                            vscode.postMessage({ command: 'alert', data: 'Hello from ${this._viewId}!' });
                        }
                    </script>
                </body>
                </html>
            `;
        }

        // 监听消息
        webviewView.webview.onDidReceiveMessage(message => {
            console.log('事件监听', message);
            if (message.command === 'showMessage') {
                vscode.window.showInformationMessage(message.text);
            }
        });
    }

    private getHtmlForWebview(webview: vscode.Webview) {
        // 获取webview脚本的URI
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(this._extensionUri, 'dist', 'webview.js'));
        console.log('Script URI:', scriptUri.toString());

        // 检查文件是否存在
        const scriptPath = path.join(this._extensionUri.fsPath, 'dist', 'webview.js');
        const scriptExists = fs.existsSync(scriptPath);
        console.log('Script file exists:', scriptExists, 'at path:', scriptPath);

        // 读取HTML模板
        const htmlPath = path.join(this._extensionUri.fsPath, 'src', 'webview', 'index.html');
        let html;

        try {
            html = fs.readFileSync(htmlPath, 'utf8');
            console.log('HTML template loaded successfully');
        } catch (error) {
            console.error('Failed to read HTML template:', error);
            // 提供一个简单的fallback HTML
            html = `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Code Extension</title>
</head>
<body>
    <div id="root">
        <h1>AI Code Extension</h1>
        <p>React应用加载中...</p>
        <button onclick="testMessage()">测试消息</button>
    </div>
    <script>
        const vscode = acquireVsCodeApi();
        function testMessage() {
            vscode.postMessage({ command: 'alert', data: 'Hello from webview!' });
        }
    </script>
    <script src="{{webviewScript}}"></script>
</body>
</html>`;
        }

        // 替换脚本路径
        html = html.replace('{{webviewScript}}', scriptUri.toString());

        return html;
    }

    public sendMessage(command: string, data?: any) {
        if (this._view) {
            this._view.webview.postMessage({ command, data });
        }
    }

}